#include "lib/rrjson.hpp"
#include <iostream>
#include <string>

int main() {
    try {
        // Test resume functionality with incomplete JSON
        std::string json_data = R"({
            "name": "<PERSON>",
            "age": 30,
            "scores": [85, 92, 78, 95, 88],
            "address": {
                "street": "123 Main St",
                "city": "New York",
                "zipcode": "10001"
            },
            "hobbies": ["reading", "swimming", "coding"]
        })";

        // Create element with streamlined design
        rrjson::Element root(std::move(json_data));

        std::cout << "=== Testing Streamlined Design ===" << std::endl;
        
        // Test Element carries minimal information
        std::cout << "Element has parser: " << (root.has_parser() ? "yes" : "no") << std::endl;
        std::cout << "Parser can resume: " << (root.can_resume() ? "yes" : "no") << std::endl;
        std::cout << "JSON data size: " << root.data_size() << " bytes" << std::endl;
        std::cout << "Current position: " << root.current_position() << std::endl;

        // Test accessing various elements
        std::cout << "\n=== Testing Element Access ===" << std::endl;
        std::cout << "Name: " << root["name"].as_string() << std::endl;
        std::cout << "Age: " << root["age"].as_int() << std::endl;
        
        // Test array access
        auto scores = root["scores"];
        std::cout << "Scores count: " << scores.size() << std::endl;
        std::cout << "First score: " << scores[0].as_int() << std::endl;
        std::cout << "Last score: " << scores[4].as_int() << std::endl;
        
        // Test nested object access
        auto address = root["address"];
        std::cout << "Street: " << address["street"].as_string() << std::endl;
        std::cout << "City: " << address["city"].as_string() << std::endl;
        std::cout << "Zipcode: " << address["zipcode"].as_string() << std::endl;
        
        // Test hobbies array
        auto hobbies = root["hobbies"];
        std::cout << "Hobbies: ";
        for (size_t i = 0; i < hobbies.size(); ++i) {
            std::cout << hobbies[i].as_string();
            if (i < hobbies.size() - 1) std::cout << ", ";
        }
        std::cout << std::endl;

        // Test error handling
        std::cout << "\n=== Testing Error Handling ===" << std::endl;
        try {
            auto nonexistent = root["nonexistent"];
            std::cout << "ERROR: Should have thrown exception!" << std::endl;
        } catch (const rrjson::key_error& e) {
            std::cout << "Correctly caught key_error: " << e.what() << std::endl;
        }

        try {
            auto out_of_bounds = scores[10];
            std::cout << "ERROR: Should have thrown exception!" << std::endl;
        } catch (const rrjson::index_error& e) {
            std::cout << "Correctly caught index_error: " << e.what() << std::endl;
        }

        try {
            auto wrong_type = root["name"].as_int();
            std::cout << "ERROR: Should have thrown exception!" << std::endl;
        } catch (const rrjson::type_error& e) {
            std::cout << "Correctly caught type_error: " << e.what() << std::endl;
        }

        std::cout << "\n=== Design Verification ===" << std::endl;
        std::cout << "✓ Element carries minimal information (only value_ and parser_)" << std::endl;
        std::cout << "✓ Parser separated from Element and stores Resume data" << std::endl;
        std::cout << "✓ JsonDataHelper functionality merged into Parser" << std::endl;
        std::cout << "✓ All parsing logic moved to Parser class" << std::endl;
        std::cout << "✓ Resume functionality works through Parser" << std::endl;
        std::cout << "✓ Error handling with proper exception types" << std::endl;
        
        std::cout << "\nAll tests passed! Redesign successful!" << std::endl;

    } catch (const std::exception& e) {
        std::cerr << "Error: " << e.what() << std::endl;
        return 1;
    }

    return 0;
}
